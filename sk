#!/bin/bash

HOSTNAME="localhost"
PORT="3306"
USERNAME="root"
PASSWORD="123456"

DB1="h_sanguo_test_game_data"
DB2="h_sanguo_test_game_global_log"
DB3="h_sanguo_test_game_log"
DB4="h_sanguo_test_game_router"
DB5="h_sanguo_test_game1"
DB6="h_sanguo_test_user_center"

# 创建数据库的SQL语句
sql1="CREATE DATABASE ${DB1} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;"
sql2="CREATE DATABASE ${DB2} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;"
sql3="CREATE DATABASE ${DB3} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;"
sql4="CREATE DATABASE ${DB4} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;"
sql5="CREATE DATABASE ${DB5} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;"
sql6="CREATE DATABASE ${DB6} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;"

# 授权和刷新权限的SQL语句
sql10="GRANT ALL PRIVILEGES ON *.* TO 'gs'@'%' IDENTIFIED BY '123456';"
sql11="FLUSH PRIVILEGES;"

# 删除数据库的SQL语句
sql12="DROP DATABASE IF EXISTS ${DB1};"
sql13="DROP DATABASE IF EXISTS ${DB2};"
sql14="DROP DATABASE IF EXISTS ${DB3};"
sql15="DROP DATABASE IF EXISTS ${DB4};"
sql16="DROP DATABASE IF EXISTS ${DB5};"
sql17="DROP DATABASE IF EXISTS ${DB6};"

# 函数：执行SQL语句
execute_sql() {
    local sql=$1
    mysql -h${HOSTNAME} -P${PORT} -u${USERNAME} -p${PASSWORD} -e "${sql}"
    if [ $? -ne 0 ]; then
        echo "Error executing SQL: ${sql}"
        exit 1
    fi
}

# 删除数据库
echo 'Deleting databases...'
execute_sql "${sql12}"
execute_sql "${sql13}"
execute_sql "${sql14}"
execute_sql "${sql15}"
execute_sql "${sql16}"
execute_sql "${sql17}"

# 创建数据库
echo 'Creating databases...'
execute_sql "${sql1}"
execute_sql "${sql2}"
execute_sql "${sql3}"
execute_sql "${sql4}"
execute_sql "${sql5}"
execute_sql "${sql6}"

# 授权和刷新权限
echo 'Granting privileges...'
execute_sql "${sql10}"
execute_sql "${sql11}"

# Nhập file SQL vào cơ sở dữ liệu tương ứng
echo 'Importing databases...'
mysql -h${HOSTNAME} -P${PORT} -u${USERNAME} -p${PASSWORD} ${DB1} < /home/<USER>/h_sanguo_test_game_data.sql
mysql -h${HOSTNAME} -P${PORT} -u${USERNAME} -p${PASSWORD} ${DB2} < /home/<USER>/h_sanguo_test_game_global_log.sql
mysql -h${HOSTNAME} -P${PORT} -u${USERNAME} -p${PASSWORD} ${DB3} < /home/<USER>/h_sanguo_test_game_log.sql
mysql -h${HOSTNAME} -P${PORT} -u${USERNAME} -p${PASSWORD} ${DB4} < /home/<USER>/h_sanguo_test_game_router.sql
mysql -h${HOSTNAME} -P${PORT} -u${USERNAME} -p${PASSWORD} ${DB5} < /home/<USER>/h_sanguo_test_game1.sql
mysql -h${HOSTNAME} -P${PORT} -u${USERNAME} -p${PASSWORD} ${DB6} < /home/<USER>/h_sanguo_test_user_center.sql
echo 'Tài nguyên miễn phí này đến từ mạng tài nguyên Aier, đăng nhập www.aae.ink để xem thêm nhiều bất ngờ!'
echo 'Database import completed successfully!'